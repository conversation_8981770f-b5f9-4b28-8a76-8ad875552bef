{"name": "freeblingportal", "version": "1.1.17", "private": true, "scripts": {"start": "node server/server.js | next dev -p 3669", "build": "next build", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.13", "@metamask/logo": "^3.1.1", "@moralisweb3/next": "^2.22.2", "@next/font": "13.1.0", "@openzeppelin/contracts": "^4.8.1", "@reactour/tour": "^3.3.0", "@sendgrid/mail": "^7.7.0", "@types/sendgrid": "^4.3.0", "axios": "^1.3.4", "body-parser": "^1.20.1", "chart.js": "^4.2.0", "cors": "^2.8.5", "cropperjs": "^1.5.13", "crypto": "^1.0.1", "dotenv": "^16.4.5", "eslint": "8.30.0", "eslint-config-next": "13.1.0", "express": "^4.18.2", "firebase": "^9.15.0", "firebase-admin": "^11.6.0", "flowbite": "^1.6.0", "formidable": "^2.1.1", "framer-motion": "^10.12.4", "fs": "^0.0.1-security", "lottie-react": "^2.3.1", "mongoose": "^6.9.1", "moralis": "^2.22.2", "multer": "^1.4.5-lts.1", "next": "13.1.0", "next-auth": "^4.22.1", "path": "^0.12.7", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-countdown": "^2.3.5", "react-dom": "18.2.0", "react-dropdown-select": "^4.9.3", "react-hook-form": "^7.41.1", "react-hot-toast": "^2.4.0", "react-responsive-modal": "^6.4.1", "react-slick": "^0.29.0", "react-spring": "^9.7.1", "react-tabs": "^6.0.0", "request": "^2.88.2", "sharp": "^0.31.3", "slick-carousel": "^1.8.1", "sqlite3": "^5.1.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.1", "telegraf": "^4.12.2", "twitter-api-v2": "^1.14.2", "uuid": "^9.0.0", "viem": "^1.1.4", "wagmi": "^1.2.1", "winston": "^3.8.2"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^2.0.1", "@types/node": "18.11.17", "@types/react": "^18.0.26", "@types/react-slick": "^0.23.10", "autoprefixer": "^10.4.13", "dotenv": "^16.0.3", "hardhat": "^2.12.7", "nodemon": "^2.0.22", "postcss": "^8.4.20", "tailwindcss": "^3.2.4", "typescript": ">=5.0.4"}}